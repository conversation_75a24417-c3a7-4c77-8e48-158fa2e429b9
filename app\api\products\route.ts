import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { getServerSession } from "next-auth"
import { ProductType } from "@prisma/client"

// GET /api/products - Fetch all products with optional filtering
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get("category")
    const type = searchParams.get("type")
    const isRetail = searchParams.get("isRetail")
    const locationId = searchParams.get("locationId")
    const search = searchParams.get("search")

    // Build where clause
    const where: any = {
      isActive: true
    }

    if (category) {
      where.category = {
        contains: category,
        mode: 'insensitive'
      }
    }

    if (type && Object.values(ProductType).includes(type as ProductType)) {
      where.type = type as ProductType
    }

    if (isRetail !== null) {
      where.isRetail = isRetail === "true"
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { sku: { contains: search, mode: 'insensitive' } },
        { barcode: { contains: search, mode: 'insensitive' } }
      ]
    }

    // Include location-specific data if locationId is provided
    const include: any = {
      locations: locationId ? {
        where: { locationId }
      } : true
    }

    const products = await prisma.product.findMany({
      where,
      include,
      orderBy: { name: 'asc' }
    })

    console.log(`✅ Fetched ${products.length} products from database`)
    return NextResponse.json({ products, total: products.length })
  } catch (error) {
    console.error("❌ Error fetching products:", error)
    return NextResponse.json({ error: "Failed to fetch products" }, { status: 500 })
  }
}

// POST /api/products - Create a new product
export async function POST(request: Request) {
  try {
    const session = await getServerSession()
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const data = await request.json()

    // Validate required fields
    if (!data.name || !data.price || !data.category) {
      return NextResponse.json({ 
        error: "Missing required fields: name, price, and category are required" 
      }, { status: 400 })
    }

    // Validate ProductType
    if (data.type && !Object.values(ProductType).includes(data.type)) {
      return NextResponse.json({ 
        error: "Invalid product type" 
      }, { status: 400 })
    }

    const product = await prisma.product.create({
      data: {
        name: data.name,
        description: data.description || null,
        price: parseFloat(data.price),
        cost: data.cost ? parseFloat(data.cost) : null,
        category: data.category,
        type: data.type || ProductType.OTHER,
        brand: data.brand || null,
        sku: data.sku || null,
        barcode: data.barcode || null,
        image: data.image || null,
        isRetail: data.isRetail || false,
        isFeatured: data.isFeatured || false,
        isNew: data.isNew || false,
        isBestSeller: data.isBestSeller || false,
        isSale: data.isSale || false,
        salePrice: data.salePrice ? parseFloat(data.salePrice) : null,
        rating: data.rating || 0,
        reviewCount: data.reviewCount || 0,
        features: data.features || [],
        ingredients: data.ingredients || [],
        howToUse: data.howToUse || []
      },
      include: {
        locations: true
      }
    })

    // Create location associations if provided
    if (data.locations && Array.isArray(data.locations)) {
      for (const locationData of data.locations) {
        await prisma.productLocation.create({
          data: {
            productId: product.id,
            locationId: locationData.locationId,
            stock: locationData.stock || 0,
            price: locationData.price ? parseFloat(locationData.price) : null
          }
        })
      }
    }

    console.log(`✅ Created product: ${product.name}`)
    return NextResponse.json({ product })
  } catch (error) {
    console.error("❌ Error creating product:", error)
    return NextResponse.json({ error: "Failed to create product" }, { status: 500 })
  }
}
