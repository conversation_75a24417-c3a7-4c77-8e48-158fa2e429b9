"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-provider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { NewProductDialog } from "@/components/inventory/new-product-dialog"
import { NewProfessionalProductDialog } from "@/components/inventory/new-professional-product-dialog"
import { StockAdjustmentDialog } from "@/components/inventory/stock-adjustment-dialog"
import { ProductEditDialog } from "@/components/inventory/product-edit-dialog"
import { CategoryManagementDialog } from "@/components/inventory/category-management-dialog"
import { ProductTransferDialog } from "@/components/inventory/product-transfer-dialog"
import { AccessDenied } from "@/components/access-denied"
import { AlertCircle, Plus, Search, Eye, EyeOff, Edit, Star, ShoppingCart, Image as ImageIcon, Settings, ArrowRightLeft, Loader2, Download, Database } from "lucide-react"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import { useCurrency } from "@/lib/currency-provider"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface Product {
  id: string
  name: string
  description?: string
  price: number
  cost?: number
  category: string
  type: string
  brand?: string
  sku?: string
  barcode?: string
  image?: string
  isRetail: boolean
  isActive: boolean
  isFeatured: boolean
  isNew: boolean
  isBestSeller: boolean
  isSale: boolean
  salePrice?: number
  rating?: number
  reviewCount: number
  features: string[]
  ingredients: string[]
  howToUse: string[]
  createdAt: string
  updatedAt: string
  locations?: ProductLocation[]
}

interface ProductLocation {
  id: string
  productId: string
  locationId: string
  stock: number
  price?: number
  isActive: boolean
  location?: {
    id: string
    name: string
  }
}

export default function InventoryPage() {
  const { currentLocation, hasPermission } = useAuth()
  const { formatCurrency } = useCurrency()
  const [search, setSearch] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [isNewProductDialogOpen, setIsNewProductDialogOpen] = useState(false)
  const [isNewProfessionalProductDialogOpen, setIsNewProfessionalProductDialogOpen] = useState(false)
  const [isStockAdjustmentDialogOpen, setIsStockAdjustmentDialogOpen] = useState(false)
  const [isProductEditDialogOpen, setIsProductEditDialogOpen] = useState(false)
  const [isCategoryManagementDialogOpen, setIsCategoryManagementDialogOpen] = useState(false)
  const [isProductTransferDialogOpen, setIsProductTransferDialogOpen] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)

  // Database-driven product data state
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isSeeding, setIsSeeding] = useState(false)

  // Fetch products from database
  const fetchProducts = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (currentLocation && currentLocation !== "all") {
        params.append("locationId", currentLocation)
      }

      const response = await fetch(`/api/products?${params.toString()}`)
      if (!response.ok) {
        throw new Error("Failed to fetch products")
      }

      const data = await response.json()
      setProducts(data.products || [])
      console.log(`✅ Fetched ${data.products?.length || 0} products from database`)
    } catch (err) {
      console.error("❌ Error fetching products:", err)
      setError(err instanceof Error ? err.message : "Failed to fetch products")
      setProducts([])
    } finally {
      setIsLoading(false)
    }
  }

  // Seed database with comprehensive product catalog
  const seedDatabase = async () => {
    setIsSeeding(true)
    try {
      const response = await fetch('/api/products/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('Failed to seed database')
      }

      const result = await response.json()
      console.log('✅ Database seeded successfully:', result)

      // Refresh products after seeding
      await fetchProducts()
    } catch (err) {
      console.error('❌ Error seeding database:', err)
      setError(err instanceof Error ? err.message : "Failed to seed database")
    } finally {
      setIsSeeding(false)
    }
  }

  // Fetch data on component mount and when location changes
  useEffect(() => {
    fetchProducts()
  }, [currentLocation])

  // Check if user has permission to view inventory page
  if (!hasPermission("view_inventory")) {
    return (
      <AccessDenied
        description="You don't have permission to view the inventory management page."
        backButtonHref="/dashboard"
      />
    )
  }

  // Get stock for current location
  const getProductStock = (product: Product): number => {
    if (!product.locations || product.locations.length === 0) return 0

    if (currentLocation === "all") {
      return product.locations.reduce((total, loc) => total + loc.stock, 0)
    }

    const locationStock = product.locations.find(loc => loc.locationId === currentLocation)
    return locationStock?.stock || 0
  }

  // Get minimum stock level (using 5 as default)
  const getMinStock = (product: Product): number => {
    return 5 // Default minimum stock level
  }

  // Filter products based on search term and active tab
  const filteredProducts = products.filter((product) => {
    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase()
      const matchesSearch =
        product.name.toLowerCase().includes(searchLower) ||
        (product.sku && product.sku.toLowerCase().includes(searchLower)) ||
        (product.barcode && product.barcode.toLowerCase().includes(searchLower)) ||
        product.category.toLowerCase().includes(searchLower)

      if (!matchesSearch) return false
    }

    // Filter by tab
    if (activeTab === "retail" && !product.isRetail) {
      return false
    }

    if (activeTab === "professional" && product.isRetail) {
      return false
    }

    if (activeTab === "low-stock") {
      const stock = getProductStock(product)
      const minStock = getMinStock(product)
      if (stock >= minStock) return false
    }

    return true
  })

  const handleAdjustStock = (product: any) => {
    setSelectedProduct(product)
    setIsStockAdjustmentDialogOpen(true)
  }

  const handleEditProduct = (product: any) => {
    setSelectedProduct(product)
    setIsProductEditDialogOpen(true)
  }

  const handleTransferProduct = (product: any) => {
    setSelectedProduct(product)
    setIsProductTransferDialogOpen(true)
  }

  // Export inventory data to CSV
  const exportInventoryData = () => {
    const csvHeaders = [
      'Product Name',
      'SKU',
      'Category',
      'Type',
      'Retail Price',
      'Cost Price',
      'Stock',
      'Min Stock',
      'Max Stock',
      'Status',
      'Location'
    ]

    const csvData = products.map(product => [
      product.name,
      product.sku,
      product.category,
      product.type,
      product.price,
      product.cost,
      product.stock,
      product.minStock,
      product.maxStock || '',
      product.isRetail ? 'Retail' : 'Professional',
      product.location
    ])

    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `inventory-${currentLocation}-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const lowStockCount = products.filter((product) => {
    const stock = getProductStock(product)
    const minStock = getMinStock(product)
    return stock < minStock
  }).length

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
            <p className="text-muted-foreground">Loading inventory data...</p>
          </div>
        </div>
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading inventory...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
          <p className="text-muted-foreground">
            {currentLocation === "all"
              ? "Manage inventory across all locations"
              : `Manage inventory at ${currentLocation === "loc1" ? "Downtown" : currentLocation === "loc2" ? "Westside" : "Northside"} location`}
          </p>
        </div>
        <div className="flex items-center gap-2">
          {products.length === 0 && !isLoading && (
            <Button
              variant="outline"
              onClick={seedDatabase}
              disabled={isSeeding}
              title="Populate database with comprehensive product catalog"
            >
              {isSeeding ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Database className="mr-2 h-4 w-4" />
              )}
              {isSeeding ? "Seeding..." : "Seed Database"}
            </Button>
          )}
          <Button
            variant="outline"
            onClick={() => exportInventoryData()}
            title="Export inventory data to CSV"
            disabled={products.length === 0}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button
            variant="outline"
            onClick={() => setIsCategoryManagementDialogOpen(true)}
          >
            <Settings className="mr-2 h-4 w-4" />
            Manage Categories
          </Button>
          {hasPermission("create_inventory") && (
            <Button onClick={() => setIsNewProductDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          )}
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Loading Inventory</AlertTitle>
          <AlertDescription>
            {error}. Please refresh the page or contact support if the issue persists.
          </AlertDescription>
        </Alert>
      )}

      {lowStockCount > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Low Stock Alert</AlertTitle>
          <AlertDescription>
            {lowStockCount} product{lowStockCount > 1 ? "s" : ""} {lowStockCount > 1 ? "are" : "is"} below the minimum
            stock level.
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader className="space-y-0 pb-2">
          <div className="flex flex-col sm:flex-row justify-between gap-4">
            <CardTitle>Product Inventory</CardTitle>
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>
          <CardDescription>Manage your salon's retail and professional products. Retail products automatically appear in the client shop.</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Products</TabsTrigger>
              <TabsTrigger value="retail">Retail & Shop</TabsTrigger>
              <TabsTrigger value="professional">Professional Use</TabsTrigger>
              <TabsTrigger value="low-stock" className="relative">
                Low Stock
                {lowStockCount > 0 && (
                  <Badge variant="destructive" className="ml-2 px-1.5 py-0.5 h-5 min-w-5 text-xs">
                    {lowStockCount}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="all" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={8} className="h-24 text-center">
                          No products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => {
                        const stock = getProductStock(product)
                        const minStock = getMinStock(product)

                        return (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.name}</TableCell>
                            <TableCell>{product.sku || "-"}</TableCell>
                            <TableCell>{product.category}</TableCell>
                            <TableCell className="text-right">
                              {product.price > 0 ? <CurrencyDisplay amount={product.price} /> : "-"}
                            </TableCell>
                            <TableCell className="text-right">
                              {product.cost ? <CurrencyDisplay amount={product.cost} /> : "-"}
                            </TableCell>
                            <TableCell className="text-center">
                              <Badge
                                variant={stock < minStock ? "destructive" : "outline"}
                                className="w-16"
                              >
                                {stock}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant={product.isRetail ? "default" : "secondary"}>
                                {product.isRetail ? "Retail" : "Professional"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex gap-1 justify-end">
                                <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                  Adjust
                                </Button>
                                <Button variant="ghost" size="sm" onClick={() => handleTransferProduct(product)}>
                                  <ArrowRightLeft className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="retail" className="m-0">
              <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <ShoppingCart className="h-5 w-5 text-blue-600" />
                  <h3 className="font-medium text-blue-900">E-commerce & Client Shop Management</h3>
                </div>
                <p className="text-sm text-blue-700">
                  Manage retail products that appear in your client portal shop. Control visibility, pricing, and product features.
                </p>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12"></TableHead>
                      <TableHead>Product</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Price</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-center">Features</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="h-24 text-center">
                          No retail products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => {
                        const stock = getProductStock(product)

                        return (
                          <TableRow key={product.id}>
                            <TableCell>
                              {product.image ? (
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="w-10 h-10 object-cover rounded border"
                                />
                              ) : (
                                <div className="w-10 h-10 bg-gray-100 rounded border flex items-center justify-center">
                                  <ImageIcon className="h-4 w-4 text-gray-400" />
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <div>
                                <div className="font-medium">{product.name}</div>
                                {product.rating && product.rating > 0 && (
                                  <div className="flex items-center gap-1 text-sm text-gray-500">
                                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                    {product.rating} ({product.reviewCount} reviews)
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>{product.sku || "-"}</TableCell>
                            <TableCell>{product.category}</TableCell>
                            <TableCell className="text-right">
                              <div>
                                {product.salePrice ? (
                                <div>
                                  <div className="line-through text-gray-500 text-sm">
                                    <CurrencyDisplay amount={product.price} />
                                  </div>
                                  <div className="text-red-600 font-medium">
                                    <CurrencyDisplay amount={product.salePrice} />
                                  </div>
                                </div>
                              ) : (
                                <CurrencyDisplay amount={product.price} />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <Badge
                              variant={stock < getMinStock(product) ? "destructive" : "outline"}
                              className="w-16"
                            >
                              {stock}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center">
                              {product.isActive ? (
                                <Eye className="h-4 w-4 text-green-600" title="Visible in shop" />
                              ) : (
                                <EyeOff className="h-4 w-4 text-gray-400" title="Hidden from shop" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex flex-wrap gap-1 justify-center">
                              {product.isFeatured && (
                                <Badge variant="default" className="text-xs">Featured</Badge>
                              )}
                              {product.isNew && (
                                <Badge variant="secondary" className="text-xs">New</Badge>
                              )}
                              {product.isBestSeller && (
                                <Badge variant="outline" className="text-xs">Best Seller</Badge>
                              )}
                              {product.isSale && (
                                <Badge variant="destructive" className="text-xs">Sale</Badge>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex gap-1 justify-end">
                              <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)} title="Edit product">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)} title="Adjust stock">
                                Stock
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="professional" className="m-0">
              <div className="mb-4 p-4 bg-amber-50 rounded-lg border border-amber-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-amber-600" />
                    <h3 className="font-medium text-amber-900">Professional Use Products</h3>
                  </div>
                  {hasPermission("create_inventory") && (
                    <Button
                      size="sm"
                      onClick={() => setIsNewProfessionalProductDialogOpen(true)}
                      className="bg-amber-600 hover:bg-amber-700"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Add Professional Product
                    </Button>
                  )}
                </div>
                <p className="text-sm text-amber-700">
                  Manage products for professional salon use only. These products will not appear in the client shop and are used for internal operations.
                </p>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Cost</TableHead>
                      <TableHead className="text-center">Stock</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          No professional products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => {
                        const stock = getProductStock(product)
                        const minStock = getMinStock(product)

                        return (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.name}</TableCell>
                            <TableCell>{product.sku || "-"}</TableCell>
                            <TableCell>{product.category}</TableCell>
                            <TableCell className="text-right">
                              {product.cost ? <CurrencyDisplay amount={product.cost} /> : "-"}
                            </TableCell>
                            <TableCell className="text-center">
                              <Badge
                                variant={stock < minStock ? "destructive" : "outline"}
                                className="w-16"
                              >
                                {stock}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                Adjust
                              </Button>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
            <TabsContent value="low-stock" className="m-0">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Name</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-center">Current Stock</TableHead>
                      <TableHead className="text-center">Min Stock</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No low stock products found.
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredProducts.map((product) => {
                        const stock = getProductStock(product)
                        const minStock = getMinStock(product)

                        return (
                          <TableRow key={product.id}>
                            <TableCell className="font-medium">{product.name}</TableCell>
                            <TableCell>{product.sku || "-"}</TableCell>
                            <TableCell>{product.category}</TableCell>
                            <TableCell className="text-center">
                              <Badge variant="destructive" className="w-16">
                                {stock}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-center">{minStock}</TableCell>
                            <TableCell>
                              <Badge variant={product.isRetail ? "default" : "secondary"}>
                                {product.isRetail ? "Retail" : "Professional"}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex gap-1 justify-end">
                                <Button variant="ghost" size="sm" onClick={() => handleEditProduct(product)}>
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" onClick={() => handleAdjustStock(product)}>
                                  Adjust
                                </Button>
                                <Button variant="ghost" size="sm" onClick={() => handleTransferProduct(product)}>
                                  <ArrowRightLeft className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      })
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <NewProductDialog open={isNewProductDialogOpen} onOpenChange={setIsNewProductDialogOpen} />

      <NewProfessionalProductDialog
        open={isNewProfessionalProductDialogOpen}
        onOpenChange={setIsNewProfessionalProductDialogOpen}
      />

      <ProductEditDialog
        open={isProductEditDialogOpen}
        onOpenChange={setIsProductEditDialogOpen}
        product={selectedProduct}
      />

      <StockAdjustmentDialog
        open={isStockAdjustmentDialogOpen}
        onOpenChange={setIsStockAdjustmentDialogOpen}
        product={selectedProduct}
        onStockAdjusted={fetchProducts}
      />

      <CategoryManagementDialog
        open={isCategoryManagementDialogOpen}
        onOpenChange={setIsCategoryManagementDialogOpen}
      />

      <ProductTransferDialog
        open={isProductTransferDialogOpen}
        onOpenChange={setIsProductTransferDialogOpen}
        product={selectedProduct}
      />
    </div>
  )
}

