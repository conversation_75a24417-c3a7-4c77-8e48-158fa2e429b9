import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// Interface for shop product (converted from inventory)
interface ShopProduct {
  id: string
  name: string
  description: string
  price: number
  salePrice?: number
  cost?: number
  category: string
  type: string
  image: string
  images: string[]
  stock: number
  minStock: number
  isNew?: boolean
  isBestSeller?: boolean
  isSale?: boolean
  isOnSale?: boolean
  features: string[]
  ingredients?: string[]
  howToUse?: string[]
  relatedProducts: string[]
  rating: number
  reviewCount: number
  sku: string
  barcode?: string
  location?: string
  isRetail: boolean
  isActive: boolean
  isFeatured?: boolean
  createdAt?: Date
  updatedAt?: Date
}

// Convert inventory product to shop product format
function convertInventoryToShopProduct(inventoryProduct: any): ShopProduct {
  // Generate category-based image
  const categoryName = inventoryProduct.categoryName || inventoryProduct.category_name || 'General'
  const productName = inventoryProduct.name || ''

  // Map categories to appropriate placeholder images
  const getProductImage = (category: string, name: string): string => {
    const categoryLower = category.toLowerCase()
    const nameLower = name.toLowerCase()

    if (categoryLower.includes('skincare') || nameLower.includes('cleanser') || nameLower.includes('serum')) {
      return '/images/products/skincare-placeholder.jpg'
    } else if (categoryLower.includes('makeup') || nameLower.includes('foundation') || nameLower.includes('lipstick')) {
      return '/images/products/makeup-placeholder.jpg'
    } else if (categoryLower.includes('hair') || nameLower.includes('shampoo') || nameLower.includes('conditioner')) {
      return '/images/products/haircare-placeholder.jpg'
    } else if (categoryLower.includes('nail') || nameLower.includes('polish')) {
      return '/images/products/nailcare-placeholder.jpg'
    } else if (categoryLower.includes('fragrance') || nameLower.includes('perfume')) {
      return '/images/products/fragrance-placeholder.jpg'
    }
    return '/placeholder.jpg'
  }

  return {
    id: inventoryProduct.id,
    name: inventoryProduct.name,
    description: inventoryProduct.description || '',
    price: inventoryProduct.retailPrice || inventoryProduct.retail_price || 0,
    salePrice: undefined,
    cost: inventoryProduct.costPrice || inventoryProduct.cost_price,
    category: categoryName,
    type: categoryName,
    image: getProductImage(categoryName, productName),
    images: [getProductImage(categoryName, productName)],
    stock: inventoryProduct.quantity || 0,
    minStock: inventoryProduct.minStockLevel || inventoryProduct.min_stock_level || 5,
    isNew: false,
    isBestSeller: Math.random() > 0.8, // Random best sellers for demo
    isSale: false,
    isOnSale: false,
    features: [],
    ingredients: [],
    howToUse: [],
    relatedProducts: [],
    rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // Random rating between 3.0-5.0
    reviewCount: Math.floor(Math.random() * 50),
    sku: inventoryProduct.sku || '',
    barcode: inventoryProduct.barcode,
    location: inventoryProduct.locationId || inventoryProduct.location_id,
    isRetail: inventoryProduct.isRetail || inventoryProduct.is_retail || true,
    isActive: true,
    isFeatured: Math.random() > 0.9, // Random featured products
    createdAt: inventoryProduct.createdAt ? new Date(inventoryProduct.createdAt) : new Date(),
    updatedAt: inventoryProduct.updatedAt ? new Date(inventoryProduct.updatedAt) : new Date()
  }
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get("locationId") || "loc1"

    // Try to get real data from server inventory storage
    try {
      const inventoryProducts = await ServerInventoryStorage.getProducts(locationId)
      
      // Filter only retail products
      const retailProducts = inventoryProducts.filter(product => 
        product.isRetail && product.quantity > 0
      )

      // Convert to shop product format
      const shopProducts = retailProducts.map(convertInventoryToShopProduct)

      console.log(`🛒 Shop API: Found ${shopProducts.length} retail products for location ${locationId}`)

      return NextResponse.json({ 
        products: shopProducts,
        total: shopProducts.length,
        locationId 
      })
    } catch (storageError) {
      console.error("Error with server storage:", storageError)
      return NextResponse.json({ error: "Failed to fetch shop products from storage" }, { status: 500 })
    }
  } catch (error) {
    console.error("Error fetching shop products:", error)
    return NextResponse.json({ error: "Failed to fetch shop products" }, { status: 500 })
  }
}
